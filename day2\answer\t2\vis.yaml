# Documentation: https://universe.roboflow.com/kabul-university-evptq/drug-name-detection/dataset/2
# Example usage: yolo train data=medical-pills.yaml
# parent
# ├── ultralytics
# └── datasets
#     └── medicine ← downloads here (8.19 MB)

# Train/val/test sets as 1) dir: path/to/imgs, 2) file: path/to/imgs.txt, or 3) list: [path/to/imgs1, path/to/imgs2, ..]
train: datasets/medicine/train
# 将采集在vis/test/下的数据用于val和test
val: ____________
test:  ____________
# 设置类别数量
nc:  ____________
names: 
  # 设置类别名称
  0:  ____________
