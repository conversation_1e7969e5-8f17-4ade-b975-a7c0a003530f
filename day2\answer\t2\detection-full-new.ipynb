{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "from PIL import Image\n", "import sys\n", "import os\n", "\n", "t2_dir= os.getcwd()\n", "custom_ultralytics_root = os.path.join(t2_dir, 'ultralytics')\n", "sys.path.insert(0, custom_ultralytics_root)\n", "\n", "import numpy as np\n", "from pathlib import Path\n", "from typing import Union\n", "import cv2\n", "from ultralytics import YOLO\n", "from ultralytics.engine.model import Model\n", "from typing import Any, Dict, List, Optional, Union\n", "from ultralytics.models import yolo\n", "from ultralytics.nn.tasks import (\n", "    ClassificationModel,\n", "    DetectionModel,\n", "    OBBModel,\n", "    PoseModel,\n", "    SegmentationModel,\n", "    WorldModel,\n", "    YOLOEModel,\n", "    YOLOESegModel,\n", ")\n", "from ultralytics.utils import ROOT, YAML"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["# from ultralytics.data.dataset import YOLODataset\n", "# from ultralytics.utils import IterableSimpleNamespace\n", "\n", "# dataset = YOLODataset(img_path=\"datasets/medicine/Images\", imgsz=640,)\n", "# hyp = IterableSimpleNamespace(mosaic=1.0, copy_paste=0.5, degrees=10.0, translate=0.2, scale=0.9)\n", "# transforms = v8_transforms(dataset, imgsz=640, hyp=hyp)\n", "# augmented_data = transforms(dataset[0])"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# class YOLO(Model):\n", "#     def __init__(self, model: Union[str, Path] = \"yolo11n.pt\", task: Optional[str] = None, verbose: bool = False):\n", "#         path = Path(model if isinstance(model, (str, Path)) else \"\")\n", "#         super().__init__(model=model, task=task, verbose=verbose)\n", "#         if hasattr(self.model, \"model\") and \"RTDETR\" in self.model.model[-1]._get_name():  # if RTDETR head\n", "#             from ultralytics import RTDETR\n", "#             new_instance = RTDETR(self)\n", "#             self.__class__ = type(new_instance)\n", "#             self.__dict__ = new_instance.__dict__\n", "\n", "#     @property\n", "#     def task_map(self) -> Dict[str, Dict[str, Any]]:\n", "#         \"\"\"Map head to model, trainer, validator, and predictor classes.\"\"\"\n", "#         return {\n", "#             \"detect\": {\n", "#                 \"model\": DetectionModel,\n", "#                 \"trainer\": yolo.detect.DetectionTrainer,\n", "#                 \"validator\": yolo.detect.DetectionValidator,\n", "#                 \"predictor\": yolo.detect.DetectionPredictor,\n", "#             },\n", "#         }"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'ultralytics.models.yolo.model.YOLO'>\n"]}], "source": ["def train():\n", "    # 使用预训练模型进行训练\n", "    model = YOLO('weights/yolov8s.pt')\n", "    \n", "    # 使用medicine数据集训练模型\n", "    model.train(data=\"medicine.yaml\", epochs=100, imgsz=640, amp=False)\n", "print(YOLO)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_img():\n", "\t# 训练好的模型权重路径\n", "    model = YOLO(\"runs/detect/train/weights/best.pt\")\n", "    # 测试图片的路径\n", "    img = cv2.imread(\"test.jpg\")\n", "    res = model(img)\n", "    ann = res[0].plot()\n", "    print(res[0])\n", "    # while True:\n", "    #     cv2.imshow(\"yolo\", ann)\n", "    #     if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "    #         break\n", "    # 设置保存图片的路径\n", "    print(\"现在开始保存图片\")\n", "    # cur_path = sys.path[0]\n", "    # print(cur_path, sys.path)\n", "\n", "    # if os.path.exists(cur_path):\n", "    #     cv2.imwrite(cur_path + \"out.jpg\", ann)\n", "    # else:\n", "    #     os.mkdir(cur_path)\n", "    #     cv2.imwrite(cur_path + \"out.jpg\", ann)\n", "\n", "    cur_path = os.getcwd()\n", "    final_path = os.path.join(cur_path, 'out.jpg')\n", "    if os.path.exists(cur_path):\n", "        cv2.imwrite(final_path, ann)\n", "    else:\n", "        os.mkdir(cur_path)\n", "        cv2.imwrite(final_path, ann)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def predict():\n", "    # Load a model\n", "    # model = YOLO('yolov8n.pt')  # 加载官方的模型权重作评估\n", "    model = YOLO('runs/detect/train/weights/best.pt')  # 加载自定义的模型权重作评估\n", "\n", "    # 评估\n", "    metrics = model.val()\n", "    # 如果要在新的数据集上测试训练结果，需要将数据集绝对路径传入，例如：\n", "    # metrics = model.val(data=“YOLOv8/.../VOC.yaml”)\n", "    print(metrics.box.map)  # map50-95\n", "    print(metrics.box.map50)  # map50\n", "    print(metrics.box.map75)  # map75\n", "    print(metrics.box.maps)  # 包含每个类别的map50-95列表\n", "    print(f\"maps shape: {metrics.box.maps.shape}\")  # 打印shape来调试\n", "\n", "    # Accessing AP75 for each category\n", "    # 检查maps的维度\n", "    if len(metrics.box.maps.shape) == 2:\n", "        # 多类别情况：maps是2D数组 (num_classes, num_iou_thresholds)\n", "        ap75_each_category = metrics.box.maps[:, 5]  # AP75对应索引5\n", "    else:\n", "        # 单类别情况：maps是1D数组 (num_iou_thresholds,)\n", "        ap75_each_category = metrics.box.maps[5] if len(metrics.box.maps) > 5 else metrics.box.map75\n", "    \n", "    print(f\"AP75 for each category: {ap75_each_category}\")\n", "\n", "    # Accessing AP75 for each category\n", "    # ap75_each_category = metrics.box.maps[:, 5]  # 利用maps矩阵可以得到AP75\n", "    # print(ap75_each_category)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Ultralytics 8.3.166 🚀 Python-3.8.16 torch-1.13.0+cu116 CUDA:0 (NVIDIA GeForce RTX 4090, 24217MiB)\n", "WARNING ⚠️ Upgrade to torch>=2.0.0 for deterministic training.\n", "\u001b[34m\u001b[1mengine/trainer: \u001b[0magnostic_nms=False, amp=False, augment=False, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=medicine.yaml, degrees=0.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=640, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=weights/yolov8s.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=train, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=None, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs/detect/train, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None\n", "medicine.yaml\n", "Overriding model.yaml nc=80 with nc=1\n", "\n", "                   from  n    params  module                                       arguments                     \n", "  0                  -1  1       928  ultralytics.nn.modules.conv.Conv             [3, 32, 3, 2]                 \n", "  1                  -1  1     18560  ultralytics.nn.modules.conv.Conv             [32, 64, 3, 2]                \n", "  2                  -1  1     29056  ultralytics.nn.modules.block.C2f             [64, 64, 1, True]             \n", "  3                  -1  1     73984  ultralytics.nn.modules.conv.Conv             [64, 128, 3, 2]               \n", "  4                  -1  2    197632  ultralytics.nn.modules.block.C2f             [128, 128, 2, True]           \n", "  5                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]              \n", "  6                  -1  2    788480  ultralytics.nn.modules.block.C2f             [256, 256, 2, True]           \n", "  7                  -1  1   1180672  ultralytics.nn.modules.conv.Conv             [256, 512, 3, 2]              \n", "  8                  -1  1   1838080  ultralytics.nn.modules.block.C2f             [512, 512, 1, True]           \n", "  9                  -1  1    656896  ultralytics.nn.modules.block.SPPF            [512, 512, 5]                 \n", " 10                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 11             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 12                  -1  1    591360  ultralytics.nn.modules.block.C2f             [768, 256, 1]                 \n", " 13                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 14             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 15                  -1  1    148224  ultralytics.nn.modules.block.C2f             [384, 128, 1]                 \n", " 16                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]              \n", " 17            [-1, 12]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 18                  -1  1    493056  ultralytics.nn.modules.block.C2f             [384, 256, 1]                 \n", " 19                  -1  1    590336  ultralytics.nn.modules.conv.Conv             [256, 256, 3, 2]              \n", " 20             [-1, 9]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 21                  -1  1   1969152  ultralytics.nn.modules.block.C2f             [768, 512, 1]                 \n", " 22        [15, 18, 21]  1   2116435  ultralytics.nn.modules.head.Detect           [1, [128, 256, 512]]          \n", "Model summary: 129 layers, 11,135,987 parameters, 11,135,971 gradients, 28.6 GFLOPs\n", "\n", "Transferred 349/355 items from pretrained weights\n", "Freezing layer 'model.22.dfl.conv.weight'\n", "\u001b[34m\u001b[1mtrain: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 70.5±45.9 MB/s, size: 32.4 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mScanning /home/<USER>/userSpace/studnet/module_B/t2_back/datasets/medicine/train/labels.cache... 1276 images, 2 backgrounds, 0 corrupt: 100%|██████████| 1276/1276 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING ⚠️ Box and segment counts should be equal, but got len(segments) = 182, len(boxes) = 1277. To resolve this only boxes will be used and all segments will be removed. To avoid this please supply either a detect or segment dataset, not a detect-segment mixed dataset.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m\u001b[1mval: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 48.4±20.8 MB/s, size: 29.9 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mval: \u001b[0mScanning /home/<USER>/userSpace/studnet/module_B/t2_back/datasets/medicine/valid/labels.cache... 365 images, 2 backgrounds, 0 corrupt: 100%|██████████| 365/365 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING ⚠️ Box and segment counts should be equal, but got len(segments) = 44, len(boxes) = 364. To resolve this only boxes will be used and all segments will be removed. To avoid this please supply either a detect or segment dataset, not a detect-segment mixed dataset.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Plotting labels to runs/detect/train/labels.jpg... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m 'optimizer=auto' found, ignoring 'lr0=0.01' and 'momentum=0.937' and determining best 'optimizer', 'lr0' and 'momentum' automatically... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m AdamW(lr=0.002, momentum=0.9) with parameter groups 57 weight(decay=0.0), 64 weight(decay=0.0005), 63 bias(decay=0.0)\n", "Image sizes 640 train, 640 val\n", "Using 8 dataloader workers\n", "Logging results to \u001b[1mruns/detect/train\u001b[0m\n", "Starting training for 100 epochs...\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      1/100      6.75G      1.847      3.717      1.579         21        640: 100%|██████████| 80/80 [00:09<00:00,  8.37it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:01<00:00,  8.98it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.218      0.349      0.174     0.0648\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      2/100      7.21G       1.83      2.428      1.659         27        640: 100%|██████████| 80/80 [00:08<00:00,  9.98it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.28it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.192      0.319      0.141     0.0571\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      3/100      7.21G      1.875      2.384      1.728         17        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.60it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364    0.00995      0.132    0.00873    0.00258\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      4/100      7.21G      1.806      2.297      1.697         21        640: 100%|██████████| 80/80 [00:07<00:00, 10.25it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364     0.0527     0.0989     0.0212    0.00625\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      5/100      7.21G      1.804      2.265      1.648         21        640: 100%|██████████| 80/80 [00:07<00:00, 10.19it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.29      0.234      0.156     0.0624\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      6/100      7.21G      1.748      2.225      1.628         17        640: 100%|██████████| 80/80 [00:07<00:00, 10.21it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.28it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.235      0.341      0.201     0.0866\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      7/100      7.21G       1.72      2.175      1.602         16        640: 100%|██████████| 80/80 [00:07<00:00, 10.21it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.171      0.332      0.189     0.0786\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      8/100      7.21G      1.639      2.057      1.571         23        640: 100%|██████████| 80/80 [00:07<00:00, 10.21it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.321      0.385      0.274      0.129\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["      9/100      7.21G      1.642      2.041      1.549         24        640: 100%|██████████| 80/80 [00:07<00:00, 10.19it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.55it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.257      0.321      0.186      0.082\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     10/100      7.21G       1.62      2.005      1.529         16        640: 100%|██████████| 80/80 [00:07<00:00, 10.19it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.63it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.352      0.405      0.288      0.145\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     11/100      7.21G      1.561      1.955      1.487         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.19it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.46it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.361      0.379      0.319      0.167\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     12/100      7.21G      1.576      1.892      1.473         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.19it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.405      0.387       0.31      0.158\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     13/100      7.21G      1.522      1.887      1.471         26        640: 100%|██████████| 80/80 [00:07<00:00, 10.16it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.53it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.394      0.437       0.36      0.189\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     14/100      7.21G      1.513      1.861      1.441         17        640: 100%|██████████| 80/80 [00:07<00:00, 10.18it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.403      0.446      0.374      0.205\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     15/100      7.21G       1.48      1.805      1.424         25        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.389      0.475        0.4      0.225\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     16/100      7.21G      1.482      1.769      1.406         16        640: 100%|██████████| 80/80 [00:07<00:00, 10.16it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.44it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.411      0.459      0.367      0.194\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     17/100      7.21G      1.482      1.763      1.432         14        640: 100%|██████████| 80/80 [00:07<00:00, 10.13it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.53it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.412      0.575      0.432      0.233\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     18/100      7.21G      1.469      1.737      1.417         15        640: 100%|██████████| 80/80 [00:07<00:00, 10.08it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.423      0.492      0.438      0.243\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     19/100      7.21G      1.458      1.719      1.391         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.07it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.48it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.364      0.558      0.414       0.22\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     20/100      7.21G      1.458      1.708       1.42         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.04it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.439      0.489      0.412      0.239\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     21/100      7.21G      1.396      1.688      1.361         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.04it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.38it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.454      0.551      0.471      0.272\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     22/100      7.21G      1.423      1.722      1.389         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.35it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.453      0.489      0.466       0.26\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     23/100      7.21G      1.384       1.64       1.37         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.46      0.489      0.446      0.248\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     24/100      7.21G      1.403      1.595       1.36         23        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.443      0.514      0.458      0.255\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     25/100      7.21G      1.402      1.671       1.37         20        640: 100%|██████████| 80/80 [00:07<00:00, 10.02it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.54      0.538      0.512      0.293\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     26/100      7.21G      1.365      1.591      1.352         20        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.37it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.505      0.588      0.519      0.295\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     27/100      7.21G      1.402      1.579      1.372         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.39it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.439      0.525      0.456      0.246\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     28/100      7.21G      1.338      1.535      1.324         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.06it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.554      0.571      0.562      0.321\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     29/100      7.21G      1.356      1.527       1.33         31        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.60it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.528      0.571      0.542      0.296\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     30/100      7.21G      1.332      1.542      1.319         22        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.61it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.514      0.613      0.527      0.297\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     31/100      7.21G      1.342      1.543      1.327         15        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.592      0.544      0.563      0.327\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     32/100      7.21G      1.325      1.477      1.311         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.15it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.52      0.597      0.533      0.296\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     33/100      7.21G      1.336      1.512      1.308         16        640: 100%|██████████| 80/80 [00:07<00:00, 10.17it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.60it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.469      0.574      0.501      0.296\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     34/100      7.21G      1.304      1.513      1.305         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.18it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.538       0.61      0.549      0.313\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     35/100      7.21G       1.29      1.468      1.283         21        640: 100%|██████████| 80/80 [00:07<00:00, 10.11it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.525      0.581      0.564      0.325\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     36/100      7.21G      1.299       1.45      1.297         17        640: 100%|██████████| 80/80 [00:07<00:00, 10.19it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.43it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.468      0.634      0.569      0.338\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     37/100      7.21G      1.272      1.431      1.285         11        640: 100%|██████████| 80/80 [00:07<00:00, 10.17it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.69it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.549      0.482      0.529      0.298\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     38/100      7.21G      1.289      1.436      1.286         22        640: 100%|██████████| 80/80 [00:07<00:00, 10.16it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.58it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.577      0.577      0.565      0.337\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     39/100      7.21G      1.265       1.38      1.279         16        640: 100%|██████████| 80/80 [00:07<00:00, 10.16it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.64it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.588       0.61      0.605      0.354\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     40/100      7.21G      1.246      1.385      1.268         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.17it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.597       0.56      0.574      0.342\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     41/100      7.21G      1.267      1.388      1.266         17        640: 100%|██████████| 80/80 [00:07<00:00, 10.15it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.49it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.608      0.519       0.58      0.328\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     42/100      7.21G      1.284      1.383      1.287         20        640: 100%|██████████| 80/80 [00:07<00:00, 10.15it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.591      0.596      0.588      0.337\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     43/100      7.21G      1.256      1.422      1.269         24        640: 100%|██████████| 80/80 [00:07<00:00, 10.17it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.601      0.574      0.593      0.348\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     44/100      7.21G      1.242      1.315      1.252         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.17it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.609      0.577      0.609      0.356\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     45/100      7.21G       1.23      1.324      1.257         16        640: 100%|██████████| 80/80 [00:07<00:00, 10.15it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.533      0.569      0.557       0.32\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     46/100      7.21G      1.222      1.304      1.241         23        640: 100%|██████████| 80/80 [00:07<00:00, 10.09it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.625      0.554      0.602      0.345\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     47/100      7.21G      1.234      1.323      1.244         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.599      0.591      0.604      0.359\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     48/100      7.21G      1.211      1.307      1.242         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.61it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.568      0.635      0.613      0.374\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     49/100      7.21G      1.195      1.288      1.231         23        640: 100%|██████████| 80/80 [00:07<00:00, 10.16it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.60it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.606      0.583      0.628      0.368\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     50/100      7.21G      1.219        1.3      1.235         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.15it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.55it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.61      0.657      0.643      0.376\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     51/100      7.21G      1.211      1.267      1.238         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.09it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.648      0.571      0.619       0.38\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     52/100      7.21G      1.182       1.24      1.224         25        640: 100%|██████████| 80/80 [00:07<00:00, 10.04it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.67it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.594      0.654      0.667      0.408\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     53/100      7.21G      1.182      1.243      1.221         16        640: 100%|██████████| 80/80 [00:07<00:00, 10.13it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.63it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.585      0.547      0.568      0.319\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     54/100      7.21G      1.158      1.215      1.204         20        640: 100%|██████████| 80/80 [00:07<00:00, 10.13it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.66it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.581       0.64      0.655      0.392\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     55/100      7.21G      1.177        1.2      1.205         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.11it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.67it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.575      0.632      0.641      0.392\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     56/100      7.21G       1.16       1.22      1.199         22        640: 100%|██████████| 80/80 [00:07<00:00, 10.08it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.625      0.574      0.634      0.387\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     57/100      7.21G      1.144      1.181      1.193         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.64it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.598      0.618      0.627      0.395\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     58/100      7.21G      1.133      1.179      1.193         17        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.61it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.607      0.626      0.622      0.379\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     59/100      7.21G      1.153      1.191      1.215         22        640: 100%|██████████| 80/80 [00:07<00:00, 10.02it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.61      0.671      0.658      0.399\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     60/100      7.21G      1.128      1.151      1.184         24        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.69it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.574      0.593      0.635      0.375\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     61/100      7.21G       1.13      1.151      1.198         14        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.67it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.639       0.61      0.663      0.412\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     62/100      7.21G      1.132       1.11       1.18         21        640: 100%|██████████| 80/80 [00:07<00:00, 10.11it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.65it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.652      0.615      0.649      0.395\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     63/100      7.21G      1.107      1.124      1.162         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.597      0.643      0.644      0.398\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     64/100      7.21G      1.112      1.091      1.164         11        640: 100%|██████████| 80/80 [00:07<00:00, 10.04it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.64it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.665       0.64      0.659      0.425\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     65/100      7.21G      1.107      1.106      1.167         17        640: 100%|██████████| 80/80 [00:07<00:00, 10.09it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.679       0.58      0.645        0.4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     66/100      7.21G      1.101        1.1      1.158         22        640: 100%|██████████| 80/80 [00:07<00:00, 10.10it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.52it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.68      0.641      0.685      0.429\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     67/100      7.21G      1.124      1.098      1.174         17        640: 100%|██████████| 80/80 [00:07<00:00, 10.10it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.55it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.601      0.634      0.649      0.402\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     68/100      7.21G      1.088      1.082       1.16         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.11it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.54it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.633      0.668      0.689      0.437\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     69/100      7.21G      1.069      1.056      1.156         21        640: 100%|██████████| 80/80 [00:07<00:00, 10.12it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.54it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.629      0.659      0.661      0.411\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     70/100      7.21G      1.095      1.047      1.156         21        640: 100%|██████████| 80/80 [00:07<00:00, 10.11it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.58it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.648      0.635      0.677      0.425\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     71/100      7.21G      1.076      1.037      1.143         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.09it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.57it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.677      0.589      0.658      0.418\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     72/100      7.21G      1.064      1.013      1.128         24        640: 100%|██████████| 80/80 [00:07<00:00, 10.12it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.50it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.588      0.668      0.674      0.433\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     73/100      7.21G       1.05      1.008       1.13         23        640: 100%|██████████| 80/80 [00:07<00:00, 10.06it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.54it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.617      0.616       0.67      0.423\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     74/100      7.21G      1.058     0.9873      1.134         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.14it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.70it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.657      0.663      0.674      0.429\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     75/100      7.21G      1.062      1.006      1.143         24        640: 100%|██████████| 80/80 [00:07<00:00, 10.16it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.70it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.608      0.644      0.667      0.414\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     76/100      7.21G      1.038      0.984      1.127         16        640: 100%|██████████| 80/80 [00:07<00:00, 10.15it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.72it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.69      0.571      0.661      0.419\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     77/100      7.21G       1.05     0.9865      1.142         21        640: 100%|██████████| 80/80 [00:07<00:00, 10.08it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.48it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.615      0.668      0.672      0.422\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     78/100      7.21G      1.029     0.9491      1.131         26        640: 100%|██████████| 80/80 [00:07<00:00, 10.08it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.55it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.656      0.662      0.684      0.419\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     79/100      7.21G      1.025     0.9376      1.115         23        640: 100%|██████████| 80/80 [00:07<00:00, 10.08it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.648      0.643      0.671      0.415\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     80/100      7.21G      1.017     0.9335      1.111         14        640: 100%|██████████| 80/80 [00:07<00:00, 10.08it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.657      0.684      0.686      0.429\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     81/100      7.21G      1.015     0.9518      1.111         16        640: 100%|██████████| 80/80 [00:07<00:00, 10.06it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.63it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.579      0.673       0.65      0.411\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     82/100      7.21G      1.007     0.9212      1.107         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.07it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.55it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.676      0.624      0.687      0.436\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     83/100      7.21G     0.9862     0.9184      1.101         20        640: 100%|██████████| 80/80 [00:07<00:00, 10.05it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.645      0.673      0.685      0.426\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     84/100      7.21G     0.9849     0.9184      1.104         21        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.56it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.665       0.65      0.687      0.434\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     85/100      7.21G      1.004     0.9284      1.107         21        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.657      0.662      0.675      0.421\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     86/100      7.21G     0.9862     0.8839      1.105         23        640: 100%|██████████| 80/80 [00:07<00:00, 10.03it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.83it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.654      0.698      0.702      0.433\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     87/100      7.21G     0.9917      0.874      1.108         18        640: 100%|██████████| 80/80 [00:07<00:00, 10.53it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.21it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.712      0.624      0.706      0.436\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     88/100      7.21G     0.9619     0.8443       1.08         19        640: 100%|██████████| 80/80 [00:07<00:00, 10.46it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.19it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.689       0.67      0.711      0.451\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     89/100      7.21G     0.9721     0.8619      1.091         13        640: 100%|██████████| 80/80 [00:07<00:00, 10.49it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.11it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.689      0.648      0.704      0.446\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     90/100      7.21G     0.9494     0.8397      1.079         14        640: 100%|██████████| 80/80 [00:07<00:00, 10.54it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.29it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.652      0.676      0.696      0.445\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Closing dataloader mosaic\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     91/100      7.21G     0.9228     0.7575      1.061         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.05it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.60it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.608      0.703      0.689      0.439\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     92/100      7.21G     0.8958     0.7198      1.048         13        640: 100%|██████████| 80/80 [00:07<00:00, 10.77it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.59it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.675      0.662      0.707      0.447\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     93/100      7.21G     0.8779     0.6849      1.042         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.45it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.709      0.637      0.708       0.45\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     94/100      7.21G     0.8804     0.6858      1.044         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.82it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.61it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.641      0.668      0.695      0.446\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     95/100      7.21G     0.8527      0.675      1.029         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.75it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.42it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.668      0.692      0.719      0.458\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     96/100      7.21G     0.8435     0.6757       1.03         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.66it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.27it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.643      0.708      0.707      0.449\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     97/100      7.21G     0.8429     0.6612      1.025         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.61it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 13.68it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.69      0.657      0.706      0.453\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     98/100      7.21G     0.8394     0.6494      1.018         11        640: 100%|██████████| 80/80 [00:07<00:00, 10.65it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.41it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.682      0.668      0.713      0.455\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["     99/100      7.21G     0.8415     0.6488      1.029         12        640: 100%|██████████| 80/80 [00:07<00:00, 10.61it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.78it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.664      0.706      0.704      0.454\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"name": "stderr", "output_type": "stream", "text": ["    100/100      7.21G     0.8304     0.6588      1.022         13        640: 100%|██████████| 80/80 [00:07<00:00, 10.37it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:00<00:00, 12.63it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364       0.64      0.734      0.717      0.459\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "100 epochs completed in 0.251 hours.\n", "Optimizer stripped from runs/detect/train/weights/last.pt, 22.5MB\n", "Optimizer stripped from runs/detect/train/weights/best.pt, 22.5MB\n", "\n", "Validating runs/detect/train/weights/best.pt...\n", "Ultralytics 8.3.166 🚀 Python-3.8.16 torch-1.13.0+cu116 CUDA:0 (NVIDIA GeForce RTX 4090, 24217MiB)\n", "Model summary (fused): 72 layers, 11,125,971 parameters, 0 gradients, 28.4 GFLOPs\n"]}, {"name": "stderr", "output_type": "stream", "text": ["                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 12/12 [00:01<00:00,  9.50it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.639      0.734      0.717      0.459\n", "Speed: 0.5ms preprocess, 1.7ms inference, 0.0ms loss, 0.2ms postprocess per image\n", "Results saved to \u001b[1mruns/detect/train\u001b[0m\n", "\n", "0: 640x640 1 medicine box, 4.1ms\n", "Speed: 0.6ms preprocess, 4.1ms inference, 0.5ms postprocess per image at shape (1, 3, 640, 640)\n", "现在开始保存图片\n", "/home/<USER>/userSpace/studnet/module_B/t2_back/test_images\n", "Ultralytics 8.3.166 🚀 Python-3.8.16 torch-1.13.0+cu116 CUDA:0 (NVIDIA GeForce RTX 4090, 24217MiB)\n", "Model summary (fused): 72 layers, 11,125,971 parameters, 0 gradients, 28.4 GFLOPs\n", "\u001b[34m\u001b[1mval: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 1923.4±708.0 MB/s, size: 34.5 KB)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[34m\u001b[1mval: \u001b[0mScanning /home/<USER>/userSpace/studnet/module_B/t2_back/datasets/medicine/valid/labels.cache... 365 images, 2 backgrounds, 0 corrupt: 100%|██████████| 365/365 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING ⚠️ Box and segment counts should be equal, but got len(segments) = 44, len(boxes) = 364. To resolve this only boxes will be used and all segments will be removed. To avoid this please supply either a detect or segment dataset, not a detect-segment mixed dataset.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 23/23 [00:01<00:00, 19.24it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["                   all        365        364      0.639      0.734      0.717      0.459\n", "Speed: 0.3ms preprocess, 1.5ms inference, 0.0ms loss, 0.3ms postprocess per image\n", "Results saved to \u001b[1mruns/detect/val\u001b[0m\n", "0.4587933147186639\n", "0.7173614700038301\n", "0.510452553196842\n", "[    0.45879]\n", "maps shape: (1,)\n", "AP75 for each category: 0.510452553196842\n"]}], "source": ["train()\n", "test_img()\n", "predict()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "0: 640x640 1 medicine box, 14.2ms\n", "Speed: 6.5ms preprocess, 14.2ms inference, 18.0ms postprocess per image at shape (1, 3, 640, 640)\n", "conf: <class 'numpy.float32'>\n", "现在开始保存图片\n"]}], "source": ["def test_img():\n", "\t# 训练好的模型权重路径\n", "    model = YOLO(\"runs/detect/train/weights/best.pt\")\n", "    # 测试图片的路径\n", "    img = cv2.imread(\"test.jpg\")\n", "    res = model(img)\n", "    ann = res[0].plot()\n", "    print('conf:', res[0].boxes.conf.cpu().numpy()[0])\n", "    # while True:\n", "    #     cv2.imshow(\"yolo\", ann)\n", "    #     if cv2.wait<PERSON><PERSON>(1) & 0xFF == ord('q'):\n", "    #         break\n", "    # 设置保存图片的路径\n", "    print(\"现在开始保存图片\")\n", "    # cur_path = sys.path[0]\n", "    # print(cur_path, sys.path)\n", "\n", "    # if os.path.exists(cur_path):\n", "    #     cv2.imwrite(cur_path + \"out.jpg\", ann)\n", "    # else:\n", "    #     os.mkdir(cur_path)\n", "    #     cv2.imwrite(cur_path + \"out.jpg\", ann)\n", "\n", "    t2_dir = os.getcwd()\n", "    t_path = os.path.join(t2_dir, 'test_images')\n", "    final_path = os.path.join(t_path, 'out.jpg')\n", "    if os.path.exists(t_path):\n", "        cv2.imwrite(final_path, ann)\n", "    else:\n", "        os.mkdir(t_path)\n", "        cv2.imwrite(final_path, ann)\n", "\n", "test_img()"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "def display_number(num, total_digits=8):\n", "    \"\"\"\n", "    在8位数码管上右对齐显示一个浮点数，支持小数点。\n", "    参数:\n", "        num: float 或 np.float32/np.float64 类型的数字\n", "        total_digits: 数码管总位数（默认8）\n", "    \"\"\"\n", "    if not isinstance(num, (float, np.floating)):\n", "        raise ValueError(\"输入必须是浮点数类型（float 或 np.float）\")\n", "\n", "    # 步骤1: 转为标准 float 并格式化为最多8位有效数字的字符串\n", "    val = float(num)\n", "\n", "    # 处理极小或极大数（科学计数法），但我们假设是普通小数\n", "    # 使用 %g 自动选择最短表示，最多8位有效数字\n", "    s = f\"{val:.7g}\"  # 最多8位有效数字\n", "\n", "    # 检查长度（去除小数点后）不能超过 total_digits\n", "    digits_only = s.replace('.', '')\n", "    if len(digits_only) > total_digits:\n", "        # 截断到最多 total_digits 位有效数字\n", "        s = f\"{val:.{total_digits-1}g}\"  # 重新格式化\n", "        digits_only = s.replace('.', '')\n", "        if len(digits_only) > total_digits:\n", "            # 再截一次（应对科学计数法）\n", "            s = s[:total_digits + (1 if '.' in s else 0)]  # 粗略截断\n", "\n", "    # 步骤2: 解析字符串，生成带小数点标记的数值列表\n", "    values = []\n", "    i = 0\n", "    while i < len(s):\n", "        ch = s[i]\n", "        if ch == '.':\n", "            if not values:\n", "                raise ValueError(\"格式错误：小数点不能在开头\")\n", "            values[-1] += 128  # 给前一个数字加小数点\n", "        elif ch.isdigit():\n", "            digit = int(ch)\n", "            values.append(digit)\n", "        else:\n", "            raise ValueError(f\"非法字符: {ch}\")\n", "        i += 1\n", "\n", "    # 步骤3: 右对齐 → 左边补 0\n", "    if len(values) > total_digits:\n", "        raise ValueError(f\"数字太多（{len(values)}位），超过数码管容量（{total_digits}位）\")\n", "\n", "    # 步骤4: 发送信号（从左到右，对应从右到左编号 008 ~ 001）\n", "    for idx, value in enumerate(values):\n", "        pos_from_right = len(values) - idx  # 右对齐\n", "        pos_str = f\"{pos_from_right:02d}\"\n", "        command = f\"tub{pos_str}{value:03d}\"\n", "        print(command)  # 替换为实际发送函数\n", "        # send_to_device(command)\n", "        time.sleep(1)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tub08128\n", "tub07005\n", "tub06001\n", "tub05000\n", "tub04004\n", "tub03005\n", "tub02002\n", "tub01006\n"]}], "source": ["display_number(0.510452553196842)"]}], "metadata": {"kernelspec": {"display_name": "instdiff", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}