{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from PIL import Image\n", "import sys\n", "import os\n", "\n", "t2_dir= os.getcwd()\n", "custom_ultralytics_root = os.path.join(t2_dir, 'ultralytics')\n", "sys.path.insert(0, custom_ultralytics_root)\n", "\n", "import numpy as np\n", "from pathlib import Path\n", "from typing import Union\n", "import cv2\n", "from ultralytics import YOLO\n", "from ultralytics.engine.model import Model\n", "from typing import Any, Dict, List, Optional, Union\n", "from ultralytics.models import yolo\n", "from ultralytics.nn.tasks import (\n", "    ClassificationModel,\n", "    DetectionModel,\n", "    OBBModel,\n", "    PoseModel,\n", "    SegmentationModel,\n", "    WorldModel,\n", "    YOLOEModel,\n", "    YOLOESegModel,\n", ")\n", "from ultralytics.utils import ROOT, YAML"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤1.数据增强方法定义\n", "请根据提示实现Mosaic数据增强方法，将“resource/task/t2/ultralytics/ultralytics/data/augment.py”文件中的_mosaic4()函数代码补充完整。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤2.数据预处理方法定义\n", "请根据提示将“resource/task/t2/ultralytics/ultralytics/data/augment.py”文件中的v8_transforms数据预处理函数代码补充完整。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤3.可视化图像数据获取及标注\n", "请根据《平台与设备使用指南》操作设备获取图像并进行标注，本步骤使用场景验证平台和标注平台实现。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤4.模型构建\n", "请根据提示将“resource/task/t2/ultralytics/ultralytics/cfg/models/v8/yolov8.yaml”文件中backbone和head部分代码补充完整。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤5.Distribution Focal Loss函数定义\n", "请根据提示将“resource/task/t2/ultralytics/ultralytics/utils/loss.py”文件中DFLoss部分代码补充完整。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤6.模型训练和预测函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def train():\n", "    # <1> 使用预训练模型进行训练，加载weights目录下预训练好的yolov8s.pt模型\n", "    model = ____________\n", "    # <2> 使用medicine数据集训练模型,将epoch数设置为100，输入图像大小设置为640\n", "    model.train(data=____________, epochs=____________, imgsz=____________, amp=False)\n", "# 开始训练\n", "train()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict():\n", "    # <3> 加载刚训练好的模型评估新采集数据集，训练好的模型在runs目录下\n", "    model = ____________\n", "    # 使用模型评估函数val评估待评估的测试集\n", "    # <4> 请参考medicine.yaml的形式构造vis.yaml，补全vis.yaml中代码\n", "    metrics = model.val(data=\"vis.yaml\")\n", "    print(metrics.box.map)  # map50-95\n", "    print(metrics.box.map50)  # map50\n", "    print(metrics.box.map75)  # map75\n", "    print(metrics.box.maps)  # 包含每个类别的map50-95列表\n", "\n", "    # Accessing AP75 for each category\n", "    if len(metrics.box.maps.shape) == 2:\n", "        # 多类别情况：maps是2D数组 (num_classes, num_iou_thresholds)\n", "        ap75_each_category = metrics.box.maps[:, 5]\n", "    else:\n", "        # 单类别情况：maps是1D数组 (num_iou_thresholds,)\n", "        ap75_each_category = metrics.box.maps[5] if len(metrics.box.maps) > 5 else metrics.box.map75\n", "    \n", "    return ap75_each_category\n", "# 输出预测结果\n", "print(predict())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤7.检测结果可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_img():\n", "\t# <1> 加载刚训练好的模型评估新采集数据集，训练好的模型在runs目录下\n", "    model = ____________\n", "    # <2> 使用cv2读取测试图像，要求使用步骤三中采集的样本\n", "    img = ____________\n", "    res = model(img)\n", "    ann = res[0].plot()\n", "    # <3> 设置保存图片的路径\n", "    cur_path = ____________\n", "    final_path = os.path.join(cur_path, 'out.jpg')\n", "\n", "    if os.path.exists(cur_path):\n", "        cv2.imwrite(final_path, ann)\n", "    else:\n", "        os.mkdir(cur_path)\n", "        cv2.imwrite(final_path, ann)\n", "# 展示可视化测试样本\n", "test_img()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 步骤8.平台展示药品检测结果"]}, {"cell_type": "markdown", "metadata": {}, "source": ["请根据任务书的安排以及提示补全/task/t2/tcp.py中的代码，并实现和数据采集及模型验证平台的通信，通过RGB等、数码管和灵巧手的反应验证测试结果。\n", "确认无误后，举手示意裁判并进行现场演示。"]}], "metadata": {"kernelspec": {"display_name": "Python (yolov8)", "language": "python", "name": "yolov8"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 4}