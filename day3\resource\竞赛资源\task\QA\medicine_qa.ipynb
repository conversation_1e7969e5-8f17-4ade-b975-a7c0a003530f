{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 1.药品知识回答"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤1.加载模型制作训练集"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from PIL import Image\n", "import json\n", "from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor\n", "from qwen_vl_utils import process_vision_info\n", "import torch\n", "\n", "# TODO: 根据自己的解压文件夹路径，确保下面三个路径正确，如果不一致可自行调整，这里需使用绝对路径以保证后续过程正常运行\n", "base_dir = '/home/<USER>/userSpace/竞赛资源/task/QA/source/药品信息'\n", "resized_dir = '/home/<USER>/userSpace/竞赛资源/task/QA/source/药品信息/resized_512'\n", "model_dir = \"/home/<USER>/userSpace/竞赛资源/task/QA/source/Qwen2-VL-2B-Instruct\"\n", "os.makedirs(resized_dir, exist_ok=True)\n", "\n", "model = Qwen2VLForConditionalGeneration.from_pretrained(model_dir, torch_dtype=\"auto\", device_map=\"auto\")\n", "processor = AutoProcessor.from_pretrained(model_dir)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1>resize函数：将图片 resize 到合适大小，填写max_size参数\n", "def resize_image(input_path, output_path, max_size=____):\n", "    try:\n", "        img = Image.open(input_path)\n", "        # <1>图像转换为RGB模式\n", "        _____________________________\n", "        # <1>按指定最大尺寸等比缩放图像，并使用抗锯齿算法保证缩放后的画质\n", "        _____________________________\n", "        os.makedirs(os.path.dirname(output_path), exist_ok=True)\n", "        img.save(output_path, format='JPEG')\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Failed to process {input_path}: {e}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_data = []\n", "\n", "# <2> 读取子文件夹图片resize并找出药品正面图 两处代码共1分\n", "for sub in os.listdir(base_dir):\n", "    sub_path = os.path.join(base_dir, sub)\n", "    if not os.path.isdir(sub_path):\n", "        continue\n", "\n", "    imgs = [fname for fname in sorted(os.listdir(sub_path))\n", "            if fname.lower().endswith(('.jpeg'))]\n", "    if not imgs:\n", "        continue\n", "\n", "    front_candidates = ________\n", "    front_name = None\n", "    for name in imgs:\n", "        if name in front_candidates:\n", "            front_name = name\n", "            break\n", "    if front_name is None:\n", "        front_name = imgs[0]\n", "\n", "    resized_paths_all = []\n", "    resized_front_path = None\n", "\n", "    for name in imgs:\n", "        orig_path = os.path.join(sub_path, name)\n", "        out_sub_dir = os.path.join(resized_dir, sub)\n", "        os.makedirs(out_sub_dir, exist_ok=True)\n", "        out_path = os.path.join(out_sub_dir, name)\n", "        success = ________________________________\n", "        if success:\n", "            uri = f\"file://{out_path}\"\n", "            resized_paths_all.append(uri)\n", "            if name == front_name:\n", "                resized_front_path = uri\n", "\n", "    if resized_front_path is None:\n", "        print(f\"子文件夹 {sub} 未能找到并 resize 出正面图片，跳过\")\n", "        continue\n", "\n", "# <3> 构造 messages，模型多图推理 4处代码共2分\n", "\n", "    user_instruction = _________________________________________________________\n", "    content = []\n", "    for p in resized_paths_all:\n", "        content.append(________________________________)\n", "    content.append({\"type\": \"text\", \"text\": user_instruction})\n", "    messages = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": content\n", "        }\n", "    ]\n", "    \n", "    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)\n", "    image_inputs, video_inputs = process_vision_info(messages)\n", "    inputs = processor(\n", "        text=[text],\n", "        images=image_inputs,\n", "        videos=video_inputs,\n", "        padding=True,\n", "        return_tensors=\"pt\"\n", "    )\n", "    inputs = inputs.to(model.device)\n", "    \n", "    with torch.no_grad():\n", "        generated_ids = model.generate(**inputs, max_new_tokens=_____)\n", "    \n", "    generated_ids_trimmed = [\n", "        out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)\n", "    ]\n", "    output_texts = processor.batch_decode(\n", "        generated_ids_trimmed,\n", "        skip_special_tokens=True,\n", "        clean_up_tokenization_spaces=False\n", "    )\n", "    answer = _______________________\n", "\n", "# <4> 构造并保存训练集，3处代码共1.5分\n", "    entry = {\n", "        \"conversations\": [\n", "            {\"from\": \"human\", \"value\": user_instruction},\n", "            {\"from\": \"gpt\", \"value\": answer}\n", "        ],\n", "        \"images\": ____________________\n", "    }\n", "    train_data.append(entry)\n", "\n", "\n", "output_json_path = _______________________\n", "with open(output_json_path, 'w', encoding='utf-8') as f:\n", "    json.dump(_______________________)\n", "\n", "print(f\"共处理 {len(train_data)} 个子文件夹，结果保存到 {output_json_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <5>  检查生成的药品信息，并在 LLaMA-Factory/data/dataset_info.json 中仿照已有数据格式补充填写新生成的json数据，并将新生成的 train.json 放入 LLaMA-Factory/data 中。共3分。\n", "\"drug_info\": {\n", "    \"file_name\": _____,\n", "    \"formatting\": _____,\n", "    \"columns\": {\n", "        \"messages\": _____,\n", "        \"images\": _____\n", "    },\n", "    \"tags\": {\n", "        \"role_tag\": _____,\n", "        \"content_tag\": _____,\n", "        \"user_tag\": _____,\n", "        \"assistant_tag\": _____\n", "    }\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤2.模型训练"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1>  填充examples/train_lora/qwen2vl_lora_sft.yaml中相应参数，7处代码各0.5分，共3.5分。\n", "\n", "### model\n", "model_name_or_path: /home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct\n", "\n", "### method\n", "stage: sft\n", "do_train: true\n", "finetuning_type: lora\n", "lora_rank: 8\n", "lora_target: all\n", "\n", "### dataset\n", "dataset: _____\n", "template: qwen2_vl\n", "cutoff_len: 2048\n", "max_samples: _____\n", "overwrite_cache: true\n", "preprocessing_num_workers: 16\n", "dataloader_num_workers: 4\n", "\n", "### output\n", "output_dir: _____\n", "logging_steps: 10\n", "save_steps: 500\n", "plot_loss: true\n", "overwrite_output_dir: true\n", "save_only_model: false\n", "report_to: none  \n", "\n", "### train\n", "per_device_train_batch_size: ___\n", "gradient_accumulation_steps: ___\n", "learning_rate: ___\n", "num_train_epochs: ___\n", "lr_scheduler_type: cosine\n", "warmup_ratio: 0.1\n", "bf16: true\n", "ddp_timeout: 180000000\n", "resume_from_checkpoint: null\n", "\n", "### eval\n", "val_size: 0.1\n", "per_device_eval_batch_size: 1\n", "eval_strategy: steps\n", "eval_steps: 500"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <2> 开始训练 llamafactory-cli train <your_train_yaml_path>, 0.5分"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤3.模型导出"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1> 确认 examples/merge_lora/llama3_lora_sft.yaml 中相应参数，进行模型导出。0.5分\n", "\n", "### model\n", "model_name_or_path: /home/<USER>/userSpace/studnet/Qwen2-VL-2B-Instruct\n", "adapter_name_or_path: _____\n", "template: qwen2_vl\n", "trust_remote_code: true\n", "\n", "### export\n", "export_dir: ans/qwen2vl_lora_sft\n", "export_size: 5\n", "export_device: auto  \n", "export_legacy_format: false"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <2> 开始导出 llamafactory-cli export <your_merge_yaml_path>，0.5分"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤4.模型能力评测"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1> 根据 Accuracy 及 规范输出数量 进行打分\n", "# 相同分数情况下可比较规范输出数量"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["| Accuracy 范围         | 得分  |\n", "|----------------------|-------|\n", "| ≤ 0.60               | 0     |\n", "| 0.60 < Accuracy ≤ 0.65 | 1     |\n", "| 0.65 < Accuracy ≤ 0.70 | 1.5   |\n", "| 0.70 < Accuracy ≤ 0.75 | 2     |\n", "| 0.75 < Accuracy ≤ 0.80 | 2.5   |\n", "| 0.80 < Accuracy ≤ 0.85 | 3     |\n", "| > 0.85               | 3.5   |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. 场景实践"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤1.模型转换、编译、部署、推理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <1> 参考 \"~/模块C/resource/rknn-llm/tree/main/examples/Qwen2-VL_Demo\"文档进行模型转换和部署推理 共3分\n", "# 模型转换（rknn、rkllm转换成功 1分）\n", "# 模型编译 1分\n", "# 模型部署、推理 1分"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import os\n", "import torch\n", "from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer\n", "import torch.nn.functional as F\n", "\n", "# 加载本地模型（加载预训练模型）\n", "path = '/home/<USER>/userSpace/studnet/home/<USER>/module_C/资源/LLaMA-Factory-0.9.1/ans/qwen2vl_lora_sft'\n", "batch = 1\n", "height = 392\n", "width = 392\n", "savepath = 'qwen2-vl-2b/qwen2_vl_2b_vision.onnx'\n", "\n", "model = Qwen2VLForConditionalGeneration.from_pretrained(\n", "    path,\n", "    torch_dtype=torch.float32,\n", "    low_cpu_mem_usage=True,\n", "    trust_remote_code=True).eval()\n", "tokenizer = AutoTokenizer.from_pretrained(path, trust_remote_code=True, use_fast=False)\n", "\n", "N = batch                           # batch size\n", "channel = 3                         # 3 for RGB\n", "H = height                         # 图像高度\n", "W = width                          # 图像宽度\n", "merge_size = 2\n", "temporal_patch_size = 2\n", "patch_size = 14\n", "grid_t = N // temporal_patch_size if N%temporal_patch_size == 0 else N // temporal_patch_size + 1\n", "grid_h = H // patch_size\n", "grid_w = W // patch_size\n", "\n", "def export_onnx(image, step):\n", "    # 根据batch大小调整输入图像的数量\n", "    if N == 1:\n", "        # <1>用repeat函数重复单张图片以匹配temporal_patch_size\n", "        images = _____\n", "    elif N % temporal_patch_size != 0:\n", "        repeat_time = temporal_patch_size - N % temporal_patch_size\n", "        repeat_image = image[-1:, ...].repeat(repeat_time, 1, 1, 1)\n", "        images = torch.cat((image, repeat_image), dim=0)\n", "    patches = images.reshape(grid_t, temporal_patch_size, channel, grid_h//merge_size, merge_size, patch_size, grid_w//merge_size, merge_size, patch_size)\n", "    patches = patches.permute(0, 3, 6, 4, 7, 2, 1, 5, 8)\n", "    flatten_patches = patches.reshape(grid_t * grid_h * grid_w, channel * temporal_patch_size * patch_size * patch_size)\n", "    model.visual.forward = forward_new(model.visual)\n", "    # 根据step参数判断是第一步还是第二步\n", "    if step == 1:\n", "        # <2>调用vision模块并传入flatten_patches和grid参数\n", "        feature = _____\n", "    else:\n", "        feature = model.visual(flatten_patches)\n", "    return feature\n", "\n", "def forward_new(self):\n", "    def tmp (hidden_states, grid_thw=None):\n", "        hidden_states = self.patch_embed(hidden_states)\n", "        if grid_thw is not None:\n", "            rotary_pos_emb = self.rot_pos_emb(grid_thw)\n", "            cu_seqlens = torch.repeat_interleave(grid_thw[:, 1] * grid_thw[:, 2], grid_thw[:, 0]).cumsum(\n", "                dim=0, dtype=torch.int32\n", "            )\n", "            cu_seqlens = F.pad(cu_seqlens, (1, 0), value=0)\n", "            np.save(\"./rotary_pos_emb.npy\", rotary_pos_emb.cpu().detach().numpy())\n", "            np.save(\"./cu_seqlens.npy\", cu_seqlens.cpu().detach().numpy())\n", "        else:\n", "            rotary_pos_emb = torch.from_numpy(np.load(\"./rotary_pos_emb.npy\")).to(dtype=hidden_states.dtype, device=hidden_states.device)\n", "            cu_seqlens = torch.from_numpy(np.load(\"./cu_seqlens.npy\")).to(dtype=torch.int32, device=hidden_states.device)\n", "        \n", "        for blk in self.blocks:\n", "            hidden_states = blk(hidden_states, cu_seqlens=cu_seqlens, rotary_pos_emb=rotary_pos_emb)\n", "\n", "        return self.merger(hidden_states)\n", "    return tmp"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导出 Vison 部分所对应的 onnx 模型，假设输入是2x3x392x392->(28x28)x(3x2x14x14)\n", "# pixel_values = torch.randn(784, 1176, device=\"cuda\", dtype=torch.float32)\n", "pixel_values = torch.randn(N, channel, H, W, device=\"cpu\", dtype=torch.float32)\n", "model.forward = export_onnx\n", "model = model.to(torch.float32).eval()\n", "\n", "print(\"========================step1========================\")\n", "print(\"Generating the rotary_pos_emb and cu_seqlens done.\")\n", "feature = model(pixel_values, 1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导出 Vison 部分所对应的 onnx 模型，假设输入是2x3x392x392->(28x28)x(3x2x14x14)\n", "# pixel_values = torch.randn(784, 1176, device=\"cuda\", dtype=torch.float32)\n", "pixel_values = torch.randn(N, channel, H, W, device=\"cpu\", dtype=torch.float32)\n", "model.forward = export_onnx\n", "model = model.to(torch.float32).eval()\n", "\n", "print(\"========================step2========================\")\n", "print(f\"Exporting the vision part of {path} to onnx format.\")\n", "os.makedirs(os.path.dirname(savepath), exist_ok=True)\n", "# <3>执行第二步，并导出onnx模型，此处需要将opset_version设置为19\n", "torch.onnx.export(_____)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from rknn.api import RKNN\n", "import numpy as np\n", "import os\n", "\n", "# <3>填写转换出的onnx模型路径\n", "model_path = _____\n", "target_platform = 'rk3588'\n", "\n", "rknn = RKNN(verbose=False)\n", "rknn.config(target_platform=target_platform, mean_values=[[0.48145466 * 255, 0.4578275 * 255, 0.40821073 * 255]], std_values=[[0.26862954 * 255, 0.26130258 * 255, 0.27577711 * 255]])\n", "rknn.load_onnx(model_path)\n", "rknn.build(do_quantization=False, dataset=None)\n", "os.makedirs(\"rknn\", exist_ok=True)\n", "rknn.export_rknn(\"./rknn/\" + os.path.splitext(os.path.basename(model_path))[0] + \"_{}.rknn\".format(target_platform))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 步骤2.板端测试"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| Accuracy 范围         | 得分  |\n", "|----------------------|-------|\n", "| ≤ 0.60               | 0     |\n", "| 0.60 < Accuracy ≤ 0.65 | 1     |\n", "| 0.65 < Accuracy ≤ 0.70 | 1.5   |\n", "| 0.70 < Accuracy ≤ 0.75 | 2     |\n", "| 0.75 < Accuracy ≤ 0.80 | 2.5   |\n", "| 0.80 < Accuracy ≤ 0.85 | 3     |\n", "| > 0.85               | 3.5   |"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <11> 每题0.5分 共2.5分"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}