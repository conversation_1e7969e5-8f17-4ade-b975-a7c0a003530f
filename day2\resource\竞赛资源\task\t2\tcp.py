import json
import socket
import time
import cv2
import threading
import numpy as np

import sys
import os
t2_dir= os.getcwd()
custom_ultralytics_root = os.path.join(t2_dir, 'ultralytics')
sys.path.insert(0, custom_ultralytics_root)
from ultralytics import YOLO
import argparse

def test_image(image):
    # TODO: 加载正确的模型路径（一般情况下，与detection.ipynb中的test_img()加载模型相同）
    model = YOLO(_____)
    res = model(image)
    return res[0].boxes.conf.cpu().numpy()[0]

def predict():
    # TODO: 将detection.ipynb中已完成的predict函数复制到此处
    _____


# 服务器地址和端口
SERVER_ADDRESS = _____  # TODO: 替换为服务器的实际 IP 地址
SERVER_PORT = 12343

exit_flag = False
JSON = {}
FRAME = None


def receive_messages(client_socket):
    """接收服务器消息的函数"""
    global exit_flag, JSON, FRAME
    while not exit_flag:
        try:
            header = client_socket.recv(6)  # 先接收为字节，不解码
            if len(header) == 0:  # 服务器断开
                print("服务器已断开连接")
                exit_flag = True
                break
            if header.startswith(b'JSONS:'):  # 文本数据标识
                data_length = int.from_bytes(client_socket.recv(4), 'big')
                json_data = client_socket.recv(data_length).decode('utf-8')
                JSON = json.loads(json_data)
            elif header.startswith(b'FRAME:'):  # 图像帧标识
                frame_size = int.from_bytes(client_socket.recv(4), 'big')
                frame_data = b""
                while len(frame_data) < frame_size:
                    packet = client_socket.recv(min(4096, frame_size - len(frame_data)))
                    if not packet:
                        break
                    frame_data += packet
                FRAME = cv2.imdecode(np.frombuffer(frame_data, dtype=np.uint8), 1)
                if FRAME is not None:
                    cv2.imwrite("received_frame.jpg", FRAME)
        except Exception as e:
            print(f"接收数据出错: {e}")
            exit_flag = True
            break
    print("接收线程已退出")


def start_client(mode=1, image_file="test.jpg"):
    """启动客户端，mode: 1, 2表示不同模式"""
    global exit_flag, JSON, FRAME

    client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        client_socket.connect((SERVER_ADDRESS, SERVER_PORT))
        print(f"连接到服务器 {SERVER_ADDRESS}:{SERVER_PORT}")
    except Exception as e:
        print(f"连接失败: {e}")
        return

    # 启动接收线程
    receive_thread = threading.Thread(target=receive_messages, args=(client_socket,))
    receive_thread.daemon = True
    receive_thread.start()

    try:
        if mode == 1:
            mode_1(client_socket)
        else:
            mode_2(client_socket, image_file)
    except KeyboardInterrupt:
        print("\n正在关闭客户端...")
    finally:
        exit_flag = True
        client_socket.close()


import time

def display_number(num, total_digits=8):
    """
    在8位数码管上右对齐显示一个浮点数，支持小数点。
    参数:
        num: float 或 np.float32/np.float64 类型的数字
        total_digits: 数码管总位数（默认8）
    """
    if not isinstance(num, (float, np.floating)):
        raise ValueError("输入必须是浮点数类型（float 或 np.float）")

    # 步骤1: 转为标准 float 并格式化为最多8位有效数字的字符串
    val = float(num)

    # 处理极小或极大数（科学计数法），但我们假设是普通小数
    # 使用 %g 自动选择最短表示，最多8位有效数字
    s = f"{val:.7g}"  # 最多8位有效数字

    # 检查长度（去除小数点后）不能超过 total_digits
    digits_only = s.replace('.', '')
    if len(digits_only) > total_digits:
        # 截断到最多 total_digits 位有效数字
        s = f"{val:.{total_digits-1}g}"  # 重新格式化
        digits_only = s.replace('.', '')
        if len(digits_only) > total_digits:
            # 再截一次（应对科学计数法）
            s = s[:total_digits + (1 if '.' in s else 0)]  # 粗略截断

    # 步骤2: 解析字符串，生成带小数点标记的数值列表
    values = []
    i = 0
    while i < len(s):
        ch = s[i]
        if ch == '.':
            if not values:
                raise ValueError("格式错误：小数点不能在开头")
            values[-1] += 128  # 给前一个数字加小数点
        elif ch.isdigit():
            digit = int(ch)
            values.append(digit)
        else:
            raise ValueError(f"非法字符: {ch}")
        i += 1

    # 步骤3: 右对齐 → 左边补 0
    if len(values) > total_digits:
        raise ValueError(f"数字太多（{len(values)}位），超过数码管容量（{total_digits}位）")

    command_list = []

    # 步骤4: 发送信号（从左到右，对应从右到左编号 008 ~ 001）
    for idx, value in enumerate(values):
        pos_from_right = len(values) - idx  # 右对齐
        pos_str = f"{pos_from_right:02d}"
        command = f"tub{pos_str}{value:03d}"
        command_list.append(command)
    return command_list


def recover(client_socket, pos_str):
    """
    复位函数：将 RGB 灯、数码管和灵巧手恢复到初始状态（熄灭或张开）
    """
    print("开始复位设备...")

    # 1. 复位 RGB 灯：熄灭灯（颜色值设为 00）
    odr_rgb = f"rgb{pos_str}00"  # 颜色设为 00（熄灭）
    client_socket.send(odr_rgb.encode('utf-8'))
    time.sleep(1)  # 每条指令间隔 1 秒

    # 2. 复位数码管：8 位数码管全部熄灭（值设为 015）
    for pos in range(1, 9):  # 位置 01 到 08
        pos_str = f"{pos:02d}"
        odr_tub = f"tub{pos_str}015"  # 015 表示熄灭
        client_socket.send(odr_tub.encode('utf-8'))
        time.sleep(1)

    # 3. 复位灵巧手：张开手
    odr_hand = "han255255255255255255255"
    client_socket.send(odr_hand.encode('utf-8'))
    time.sleep(1)
    print("设备已复位。")


def mode_1(client_socket):
    """模式 1：评估数据后与服务端通信"""
    print("模式 1：开始评估数据")
    pre= predict()  # 对当前帧推理
    time.sleep(2)
    # RGB2号显示为绿色，数码管展示类别的ap75结果，灵巧手比出剪刀
    # <1>rgb2号绿色
    odr_rgb = _____
    client_socket.send(odr_rgb.encode('utf-8'))
    time.sleep(1)
    # <1>数码管显示当前pre值，例：0.63显示为63
    command_list = _____
    for odr_tub in command_list:
        client_socket.send(odr_tub.encode('utf-8'))
        time.sleep(1)
    # <1>灵巧手展示剪刀手势
    odr_hand = _____
    client_socket.send(odr_hand.encode('utf-8'))
    input("请按任意键退出")
    recover(client_socket, odr_rgb[3:5])


def mode_2(client_socket, image_file="test.jpg"):
    """模式 2：识别图片展示置信度"""
    print("模式 2：正在识别图片...")
    image = cv2.imread(image_file)
    if image is None:
        print(f"无法读取图像: {image_file}")
    else:
        result = test_image(image)
        # 若评估结果小于阈值，认为未达成任务，RGB显示为红色，数码管展示置信度，灵巧手比出拳头
        # <2>rgb3号灯红色
        odr_rgb = _____
        client_socket.send(odr_rgb.encode('utf-8'))
        time.sleep(1)
        # <2>数码管显示当前result值，例：0.42显示为42
        command_list = _____
        for odr_tub in command_list:
            client_socket.send(odr_tub.encode('utf-8'))
            time.sleep(1)
        # <2>灵巧手展示握拳手势
        odr_hand = _____
        client_socket.send(odr_hand.encode('utf-8'))
        time.sleep(1)
        input("请按任意键退出")
        recover(client_socket, odr_rgb[3:5])


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="客户端运行模式选择")
    parser.add_argument('--mode', type=int, required=True, choices=[1, 2])
    parser.add_argument('--image_file', type=str, default="test.jpg")
    args = parser.parse_args()

    start_client(mode=args.mode, image_file=args.image_file)


