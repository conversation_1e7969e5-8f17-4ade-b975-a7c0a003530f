中华人民共和国第三届职业技能大赛
人工智能工程技术任务书

模块B、计算机视觉技术应用
一、任务要求
根据项目要求完成人工智能计算机视觉技术应用代码开发，将任务过程中填写的代码截图和每个步骤的输出结果粘贴在“竞赛任务应答书.docx”的指定位置中（如某一步骤无输出，应填“无”），其余过程文件按要求放置于work文件夹的指定位置。最后按照《选手指引》要求将“竞赛任务应答书.docx”存放于work文件夹，并导出压缩包，保存至指定位置。  
二、任务环境
硬件资源：高性能GPU计算机、场景验证平台、人工智能模型训练系统、人机协同数据标注平台；
软件资源：“竞赛资源.zip”，pytorch深度学习框架；
三、任务说明
本次任务中，在ipynb文件中运行各单元格时，选择内核时应该选择python环境module_B_env。
使用python命令运行各py文件时，需要在对应文件所在目录打开终端，并使用正确环境（应该为module_B_env环境，在终端执行命令conda activate module_B_env即可切换）
1、病症分类任务
病症分类任务对医疗体系具有核心枢纽作用。病症分类通过系统化整合临床症状、病理特征及病因学数据，为精准诊断提供标准化框架，显著降低误诊率。病症分类还可以优化临床决策，支撑分级诊疗与急诊分诊流程，缩短重症患者救治时间窗。同时赋能公共卫生管理，通过实时分类监测传染病暴发和慢性病分布，指导资源调配与防控政策制定。最终构建从个体精准治疗到群体健康管理的智能闭环，成为现代医疗不可或缺的基础设施。
进入人工智能模型训练系统，打开“task”文件夹，文件夹中提供了所需的全部任务文件。请打开“task/t1/classification.ipynb”文件，在该文件内编写代码完成以下操作：
（1）步骤1.图像数据增强方法定义
部分代码已给出，请根据提示实现随机擦除（RandomErasing）数据增强方法，将代码补充完整。
在训练过程中，随机擦除以概率p执行。随机擦除随机选择图像中的矩形区域Ie，并用随机值替换原本的像素值。假设训练图片的大小为W×H，则面积为S。随机初始化要擦除矩形区域的面积为Se，其中Se在指定范围Randsl,sh×S内。擦除矩形区域的长宽比re随机初始化在指定r1,r2范围内。矩形区域Ie的宽高分别为We=Sere，He=Se×re。然后我们随机选择图片I中的一个点xe,ye，如果xe+We≤W并且ye+He≤H，则选择擦除矩形区域为xe,ye,xe+We,ye+He，否则重复上述过程直到选到合适的Ie。

①请在<1>处补充代码，使用random中提供的接口获取随机数，并根据Algorithm 1伪代码的描述，判断是否对图像进行随机擦除的数据增强操作。
②请在<2>处补充代码，根据Algorithm 1中给出的随机擦除区域Ie的计算公式，计算随机擦除区域的宽和高。
③题目中给出了擦除区域的随机值生成方法_get_pixels()，请在<3>处补充代码，利用_get_pixels()生成的随机像素值替换随机擦除区域Ie原本的像素值。
将该步骤全部输出结果进行截图并保存在“竞赛任务应答书.docx”中，将<1><2><3>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（2）步骤2.图像数据预处理
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，分别定义训练集和测试集的数据预处理操作。训练和测试过程均使用torchvision.transforms中提供的接口实现以下操作：定义训练集和测试集数据预处理操作，将数据大小重置为(224, 224)，并转换为tensor数据，对数据进行归一化处理，将各通道的均值和标准差均设置为0.5，最后使用torchvision.transforms.Compose整合以上预处理操作。除上述操作外，单独在训练即预处理的最后一步添加随机擦除的数据增强方法。
②请在<2>处补充代码，使用torchvision.transforms中提供的接口对数据增广使用的样本进行增强：剪裁数据中心点周围（144,144）大小区域，对数据进行水平翻转，对数据进行30度的随机旋转，并进行和①中训练集相同的预处理操作。
③请在<3>处补充代码，使用torchvision.datasets中的接口读取训练集和测试集的数据，同时用定义好的预处理操作对数据进行预处理。
④请在<4>处补充代码，使用torchvision.datasets中的接口再次读取训练集并将其作为增广数据进行预处理。
⑤请在<5>处补充代码，使用torch.utils.data.DataLoader创建训练集和测试集的Dataloader对象，要求设置Dataloader的dataset 、batch_size和shuffle三个参数，且仅训练时在每个epoch重新打乱数据。
将该步骤全部输出结果进行截图并保存在“竞赛任务应答书.docx”中，将<1>至<5>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（3）步骤3.损失函数构造
部分代码已给出，请根据提示，将代码补充完整。
交叉熵损失函数在分类任务中起着至关重要的作用，通过最小化损失函数，神经网络能够逐步学习到更精确的分类边界。多分类的交叉熵损失函数的计算公式如下：

其中yi是真实标签，zi为第i类的logits值，表示网络输出的第i类的得分。
①对于多分类问题，通常会先通过Softmax将模型输出的logits转化为概率分布，然后再计算交叉熵损失。请在<1>处补充代码，使用torch中预设的Softmax函数处理输入的预测结果pred。
②请在<2>处补充代码，使用torch.nn.functional中预设的one_hot函数将真值标签转化为独热编码，并将数据类型转为float。
③请在<3>处补充代码，根据公式使用torch中预设的求和函数计算损失值。
将该步骤全部输出结果进行截图并保存在“竞赛任务应答书.docx”中，将<1>至<3>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（4）步骤4.分类模型设置
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用未经预训练的resnet18作为分类预测模型，需使用torchvision.models接口调用resnet18。
②请在<2>处补充代码，设置模型全连接层的输入和输出维度，输入维度保持不变，输出维度定义为类别数量（正常和肺炎两种），最后将模型移动到和数据相同的设备上。
③请在<3>处补充代码，选择步骤4中构造的交叉熵损失作为损失函数。
④请在<4>处补充代码，使用torch.optim中预设的Adam优化器，仅需要设置用于迭代优化的参数params和学习率lr。
将该步骤全部输出结果进行截图并保存在“竞赛任务应答书.docx”中，将<1>至<4>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（5）步骤5.模型训练和验证函数构造
部分代码已给出，请根据代码提示，将代码补充完成。
①请在<1>处补充代码，构造模型训练函数，将模型设置为训练模式。
②请在<2>处补充代码，按步骤计算当前batch的损失函数，将当前batch的梯度初始化为零，反向传播求梯度，然后更新所有参数。
③请在<3>处补充代码，计算该epoch的平均loss。
④请在<4>处补充代码，构造模型测试函数，将模型设置为评估模式。
⑤请在<5>处补充代码，判断预测结果是否准确，若准确则增加准确预测数。
⑥请在<6>处补充代码，根据预测结果计算模型测试准确率。
将该步骤全部输出结果进行截图并保存在“竞赛任务应答书.docx”中，将<1>至<6>处补充的代码截图并保存在“竞赛任务应答书.docx”中。请将保存的模型“model_resnet18.pth”拷贝一份放置“work”文件夹的“model”目录。
（6）步骤6.模型测试结果可视化
部分代码已给出，请根据代码提示，将代码补充完成。
①分类模型输入要求图像为三通道彩色图像，请在<1>处补充代码，将单通道灰度图转化为三通道的彩色图像。
②请在<2>处补充代码，进行数据预处理，要求与测试集的预处理方式一致。
③请在<3>处补充代码，读取模型并将模型设置为评估模式。
④请在<4>处补充代码，预测待测样本的分类结果，按步骤预处理读取的图像，将预处理好的单通道图像转化为三通道彩色图像，使用unsqueeze在第0维扩大图像维度。
⑤请在<5>处补充代码，使用plt设置1行4列的子图展示预测结果，通过定义的idx表示当前子图位置，并在循环的末尾处更新位置标识idx。
将该步骤全部输出结果进行截图并保存在“竞赛任务应答书.docx”中，将<1>至<5>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（7）步骤7.模型分类性能可视化
部分代码已给出，请根据代码提示，将代码补充完成。
①请在<1>处补充代码，读取测试集数据并同时进行预处理，使用步骤2中定义的transform_test进行预处理；使用torch.utils.data.DataLoader创建测试集的Dataloader对象，要求设置Dataloader的dataset 、batch_size和shuffle三个参数，且不重新打乱数据。
②请在<2>处补充代码，读取分类模型并将模型设置为评估模式。
③请在<3>处补充代码，为获取测试样本特征，使用定义好的恒等变换替换model_feat的全连接层，并将该模型设置为评估模式。
④请在<4>处补充代码，使用cat函数将列表feats拼接为一个二维张量。
⑤请在<5>处补充代码，定义TSNE，要求设置n_components、init和random_state三个参数，要求TSNE的嵌入空间维度为2、使用pca降维且随机数种子为0。
⑥请在<6>处补充代码，使用tsne将feats投影到嵌入空间，并返回转换结果。
将该步骤全部输出结果进行截图并保存在“竞赛任务应答书.docx”中，将<1>至<6>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
注：编写完成之后，需要将“classification.ipynb”文件拷贝至“work”文件夹的“code”目录下。
2、药品检测任务
药品检测任务对医疗健康体系具有基础性支撑作用。它通过精确区分药品属性保障用药安全，显著降低误服和配伍风险。还能够提升医疗效率，支撑药房智能管理和处方自动化处理。同时强化公共卫生治理，为药品不良反应监测、抗生素耐药性防控及监管决策提供标准化数据基础，最终构建起连接药物研发、临床应用与全民健康管理的系统性保障网络。
进入人工智能模型训练系统，打开“task”文件夹，文件夹中提供了所需的全部任务文件。请打开“task/t2/detection.ipynb”文件，在该文件内编写代码完成以下操作：
（1）步骤1.数据增强方法定义
部分代码已给出，请根据提示实现Mosaic数据增强方法，将代码补充完整，待补全的代码位置为“task/t2/ultralytics/ultralytics/data/augment.py”中的_mosaic4()函数。
Mosaic数据增强利用了四张图片，对四张图片进行拼接，每一张图片都有其对应的框，将四张图片拼接之后就获得一张新的图片，同时也获得这张图片对应的框，然后将这张新的图片传入到神经网络当中去学习。下面将构造通过2x2张图像实现mosaic的方法。

图1 Mosaic数据增强
①请在<1>处补充代码，根据指示初始化一个大小为（2*s，2*s，通道数）且像素值为114的数组画布，其中s为待拼接图像的固定（重构）边长。
②第一张图所在的画布中左上区域的位置，图像的右下角点与中心点重合，请在<2>处补充代码，计算画布坐标系下矩形框的左上角点与右下角点坐标。
③第二张图所在的画布中右上区域的位置，图像的左下角点与中心点重合，请在<3>处补充代码，计算画布坐标系下矩形框的左上角点与右下角点坐标。
④第三张图所在的画布中左下区域的位置，图像的右上角点与中心点重合，请在<4>处补充代码，计算画布坐标系下矩形框的左上角点与右下角点坐标。
⑤第四张图所在的画布中右下区域的位置，图像的左上角点与中心点重合，请在<5>处补充代码，计算画布坐标系下矩形框的左上角点与右下角点坐标。
⑥请在<6>处补充代码，将原图像裁剪后贴入对应画布中。
该步骤无输出结果，可在“竞赛任务应答书.docx”中填写“无”，将<1>至<6>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（2）步骤2.数据集预处理定义
部分代码已给出，请根据提示将“task/t2/ultralytics/ultralytics/data/augment.py”文件中的v8_transforms数据预处理函数代码补充完整。
①请在<1>处补充代码，使用Compose函数将mosaic和随机扰动整合成前处理方法pre_transform。
该步骤无输出结果，可在“竞赛任务应答书.docx”中填写“无”，将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（3）步骤3.可视化图像数据获取及标注
请根据《人工智能工程技术_平台与设备使用指南》操作设备获取图像，进行数据传输，并进行标注。
①启动数据采集及模型验证平台，通过平台的摄像头获取提供的药品道具图像数据各1张，将获取的图像数据传输回竞赛的主机，保存到竞赛系统环境的“task/t2/datasets/vis/test/images”文件夹中，同时将获取的药品道具图像，在“竞赛任务应答书.docx”中各保存一张；
②使用标注平台对①中获取的图像数据进行标注，类别标签设置为“medicine box”，仅需使用这种标签对图像进行标注。操作过程中仅需截图保存一张图像的标注界面，将标注好的全部数据导出为YOLO格式，将导出数据保存到竞赛系统环境的“task/t2/datasets/vis/test/labels”文件夹中，截图展示该文件夹中的内容。
将上述过程中的截图保存在“竞赛任务应答书.docx”中。将整个test文件夹拷贝保存至“work”文件夹的“data”目录下。
（4）步骤4.模型构建
部分代码已给出，请根据提示将“task/t2/ultralytics/ultralytics/cfg/models/v8/yolov8.yaml”文件中backbone和head部分代码补充完整。

图2 Yolov8框架图
①根据backbone的结构补全空白部分，module表示该层使用的模块的名称，args表示这个模块需要传入的参数[通道数，卷积核大小，步长]。请在<1>处补充代码，根据图示设置P1、P2、P3、P4、P5层的module和args。
②根据head的结构补全空白部分。请在<2>处补充代码补全上采样层，要求设置输出的尺寸是输入尺寸的2倍，并使用最近邻插值算法‘nearest’作为上采样算法为。
该步骤无输出结果，可在“竞赛任务应答书.docx”中填写“无”，将<1><2>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（5）步骤5. Distribution Focal Loss函数定义
部分代码已给出，请根据提示将“task/t2/ultralytics/ultralytics/utils/loss.py”文件中DFLoss部分代码补充完整。
Yolov8引入Anchor-Free的基于中心点的方法后，模型从输出“锚框大小偏移量(offest)”变为"预测目标框左、上、右、下边框距目标中心点的距离(ltrb = left, top, right, bottom)"。为配合Anchor-Free方法，在v8中增加了DFL损失。DFL以交叉熵的形式，去优化与标签最接近的一左一右2个位置的概率，从而让网络更快的聚焦到目标位置及邻近区域的分布。损失函数计算公式如下：
DFLSi,Si+1=−yi+1−ylogSi+y−yilogSi+1
①请在<1>处补充代码，分别计算预测分布与左右目标值交叉熵损失并求和，要求交叉熵损失时需将目标值的左右边界转换为一维，且不进行归约直接返回每个样本的损失值(通过设置reduction参数实现)。
该步骤无输出结果，可在“竞赛任务应答书.docx”中填写“无”，将<1>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（6）步骤6. 模型训练和预测函数
部分代码已给出，请根据提示，将代码补充完整。
①请在<1>处补充代码，使用预训练模型进行训练，加载weights目录下预训练好的yolov8s.pt模型。
②请在<2>处补充代码，使用medicine数据集训练模型,将epoch数设置为100，输入图像大小设置为640。需要注意，这里训练模型时，会在run/detect的train目录下存放模型文件，目录已存在且不为空，那么会创建为train1、train2...以此类推，选手可以每次训练时删除所有train文件夹，以保证最后生成的模型文件总是在train目录中（无数字后缀），或正确选择自己想要的某一次训练模型。
③请在<3>处补充代码，加载刚训练好的模型评估新采集数据集，训练好的模型在runs目录下。
④请在<4>处补充代码，请参考medicine.yaml的形式构造vis.yaml，补全vis.yaml中代码。
将该步骤全部输出结果进行截图并保存在“竞赛任务应答书.docx”中，将<1>至<4>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（7）步骤7. 检测结果可视化
部分代码已给出，请根据提示，将test_img()代码补充完整。
①请在<1>处补充代码，加载刚训练好的模型评估新采集数据集，训练好的模型在runs目录下。
②请在<2>处补充代码，使用cv2读取测试图像，要求使用步骤三中采集的样本。
③请在<3>处补充代码，设置保存图片的路径，使用os方法获取当前工作目录的绝对路径。
将该步骤全部输出结果进行截图并保存在“竞赛任务应答书.docx”中，将<1>至<3>处补充的代码截图并保存在“竞赛任务应答书.docx”中。
（8）步骤8.平台展示药品检测结果
请补充完全task/t2/tcp.py中的全部代码。
	首先根据已有信息填补TODO提示位置的代码，包括：
test_image（）函数正确的模型加载（一般情况下，应与detection.ipynb中的test_img()加载模型相同）；
predict（）函数的补全（该函数直接复制detection.ipynb中填写好predict函数的即可）；
填写服务器端的ip地址（可以在数据采集及模型验证平台上的终端中运行命令ifconfig获取）。
然后：
①请在<1>处补充代码，使得2号RGB灯显示为绿色，调用给出的函数使得数码管能展示类别的ap75结果，灵巧手比出剪刀。
②请在<2>处补充代码，使得3号RGB显示为红色，调用给出的函数使得数码管展示预测的置信度结果，灵巧手比出拳头。
确保已在数据采集及模型验证平台上，打开两个终端分别运行下面两条指令： 
roslaunch linker_hand_sdk_ros linker_hand_l7.launch 
roslaunch bring_up bring_up.launch
然后运行如下指令以启动通信程序：
cd /home/<USER>/aib_ws/src/main/scripts && python3 tcp_sensor.py
上述操作完成后，在主机tcp.py所在目录下打开终端，执行conda activate module_B_env切换到对应环境。分别执行命令。
python tcp.py --mode 1
和
python tcp.py --mode 2
以验证①和②的结果（每次命令执行完成后，数据采集及模型验证平台会结束通信程序，需要重新运行指令启动）。
该步骤的输出结果和代码截图不必保存在“竞赛任务应答书.docx”中。
确认操作无误后，请向裁判示意，并演示①和②对应的操作，由裁判进行记录打分。
